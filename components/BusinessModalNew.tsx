'use client';

import React, { useState, useEffect } from 'react';
import { Business, useBusinessStore } from '@/store/businessStore';
import { useAuthStore } from '@/store/authStore';

interface BusinessModalProps {
  business: Business | null;
  isOpen: boolean;
  onClose: () => void;
}

interface CampaignData {
  id: string;
  title: string;
  month: string;
  status: string;
  budget: number;
  spent_amount: number;
  totalCriadores: number;
  created_at: string;
}

export default function BusinessModalNew({ business, isOpen, onClose }: BusinessModalProps) {
  const { moveBusinessStage } = useBusinessStore();
  const { user } = useAuthStore();
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(business?.journeyStage || '');
  const [campaigns, setCampaigns] = useState<CampaignData[]>([]);
  const [isLoadingCampaigns, setIsLoadingCampaigns] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editData, setEditData] = useState<any>({});

  // Buscar campanhas do negócio
  const fetchCampaigns = async (businessId: string) => {
    setIsLoadingCampaigns(true);
    try {
      const response = await fetch(`/api/supabase/campaigns?business_id=${businessId}`);
      const result = await response.json();

      if (result.success) {
        setCampaigns(result.data || []);
      } else {
        console.error('Erro ao buscar campanhas:', result.error);
        setCampaigns([]);
      }
    } catch (error) {
      console.error('Erro ao buscar campanhas:', error);
      setCampaigns([]);
    } finally {
      setIsLoadingCampaigns(false);
    }
  };

  // Atualizar o status local quando o business mudar
  useEffect(() => {
    if (business) {
      setCurrentStatus(business.journeyStage || '');
      setEditData({
        businessName: business.businessName || '',
        categoria: business.categoria || '',
        plano: (business as any).planoAtual || business.plano || '',
        comercial: (business as any).comercial || '',
        cidade: (business as any).cidade || '',
        prospeccao: (business as any).prospeccao || '',
        responsavel: business.responsavel || '',
        whatsapp: (business as any).whatsappResponsavel || '',
        instagram: (business as any).instagram || '',
        notes: (business as any).notes || ''
      });

      // Buscar campanhas se tiver ID
      if (business.id) {
        fetchCampaigns(business.id);
      }
    }
  }, [business]);

  if (!isOpen || !business) return null;

  // Opções de status do Kanban com Material Design
  const statusOptions = [
    { value: 'Reunião Briefing', label: 'Reunião Briefing', icon: '📋', color: 'bg-blue-50 text-blue-700 border-blue-200' },
    { value: 'Agendamentos', label: 'Agendamentos', icon: '📅', color: 'bg-amber-50 text-amber-700 border-amber-200' },
    { value: 'Entrega Final', label: 'Entrega Final', icon: '✅', color: 'bg-green-50 text-green-700 border-green-200' }
  ];

  const handleStatusChange = async (newStatus: string) => {
    if (newStatus === currentStatus || isUpdatingStatus) return;

    setIsUpdatingStatus(true);

    try {
      console.log(`🔄 Atualizando status de "${business.businessName}": ${currentStatus} → ${newStatus}`);

      // Atualizar via API (audit log)
      const response = await fetch('/api/update-business-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: business.businessName,
          oldStatus: currentStatus,
          newStatus: newStatus,
          user: user?.email || 'Sistema'
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Status atualizado no audit log');

        // Atualizar no store local (Kanban)
        moveBusinessStage(business.id, newStatus as Business['journeyStage']);

        // Atualizar estado local
        setCurrentStatus(newStatus);

        console.log('✅ Status atualizado no Kanban');
      } else {
        console.error('❌ Erro ao atualizar status:', result.error);
        alert(`Erro ao atualizar status: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Erro ao atualizar status:', error);
      alert('Erro ao atualizar status. Tente novamente.');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusOption = (status: string) => {
    return statusOptions.find(option => option.value === status) || statusOptions[0];
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatWhatsAppLink = (whatsapp: string): string | null => {
    if (!whatsapp) return null;
    const cleanNumber = whatsapp.replace(/[^\d]/g, '');
    if (cleanNumber.length >= 10) {
      return `https://wa.me/55${cleanNumber}`;
    }
    return null;
  };

  const handleWhatsAppClick = () => {
    const whatsappNumber = (business as any).whatsappResponsavel || business.whatsapp;
    const whatsappLink = formatWhatsAppLink(whatsappNumber);
    if (whatsappLink) {
      window.open(whatsappLink, '_blank');
    }
  };

  const openInstagram = () => {
    const instagram = (business as any).instagram;
    if (instagram) {
      const username = instagram.replace('@', '');
      window.open(`https://instagram.com/${username}`, '_blank');
    }
  };

  const handleEditChange = (field: string, value: string) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveChanges = async () => {
    try {
      // Aqui você implementaria a API para salvar as mudanças
      console.log('Salvando mudanças:', editData);
      setIsEditMode(false);
      // Atualizar o business no store se necessário
    } catch (error) {
      console.error('Erro ao salvar:', error);
    }
  };

  const renderEditableField = (label: string, field: string, value: string, type: 'text' | 'select' = 'text', options?: string[]) => {
    // Garantir que sempre temos um valor definido
    const safeValue = value || '';
    const editValue = editData[field] !== undefined ? editData[field] : safeValue;

    return (
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <label className="text-sm font-medium text-blue-600 uppercase tracking-wide">{label}</label>
        {isEditMode ? (
          type === 'select' ? (
            <select
              value={editValue}
              onChange={(e) => handleEditChange(field, e.target.value)}
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Selecione...</option>
              {options?.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              value={editValue}
              onChange={(e) => handleEditChange(field, e.target.value)}
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={`Digite ${label.toLowerCase()}`}
            />
          )
        ) : (
          <p className="text-base text-gray-900 mt-1">{safeValue || 'Não definido'}</p>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop com blur */}
      <div 
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300"
        onClick={onClose}
      />
      
      {/* Modal Container */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-6xl bg-white rounded-3xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100 max-h-[95vh] overflow-hidden">
          
          {/* Header Melhorado */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">{business.businessName}</h2>
                    <div className="flex items-center space-x-4 text-blue-100">
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        {business.categoria || 'Categoria não definida'}
                      </span>
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {(business as any).cidade || 'Cidade não informada'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Métricas Rápidas */}
                <div className="flex items-center space-x-6 mt-4">
                  <div className="bg-white/10 rounded-lg px-3 py-2">
                    <div className="text-xs text-blue-200">Campanhas</div>
                    <div className="text-lg font-bold">
                      {isLoadingCampaigns ? '...' : campaigns.length}
                    </div>
                  </div>
                  <div className="bg-white/10 rounded-lg px-3 py-2">
                    <div className="text-xs text-blue-200">Status</div>
                    <div className="text-sm font-medium">{currentStatus}</div>
                  </div>
                  <div className="bg-white/10 rounded-lg px-3 py-2">
                    <div className="text-xs text-blue-200">Plano</div>
                    <div className="text-sm font-medium">{business.plano || 'Não definido'}</div>
                  </div>
                </div>
              </div>

              {/* Botões do Header */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsEditMode(!isEditMode)}
                  className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                  title={isEditMode ? 'Cancelar Edição' : 'Editar Negócio'}
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {isEditMode ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    )}
                  </svg>
                </button>
                <button
                  onClick={onClose}
                  className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Content com scroll */}
          <div className="p-6 max-h-[calc(95vh-200px)] overflow-y-auto">
            <div className="max-w-4xl mx-auto">

              {/* Seção de Campanhas */}
              <div className="mb-6 bg-blue-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Campanhas ({campaigns.length})
                </h3>

                {isLoadingCampaigns ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                    <span className="text-blue-700">Carregando campanhas...</span>
                  </div>
                ) : campaigns.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {campaigns.map((campaign) => (
                      <div key={campaign.id} className="bg-white rounded-lg p-4 border border-blue-200 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-gray-900">{campaign.title}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            campaign.status === 'Finalizado' ? 'bg-green-100 text-green-800' :
                            campaign.status === 'Entrega final' ? 'bg-blue-100 text-blue-800' :
                            campaign.status === 'Agendamentos' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {campaign.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 space-y-1">
                          <div className="flex justify-between">
                            <span>Mês:</span>
                            <span className="font-medium">{campaign.month}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Criadores:</span>
                            <span className="font-medium">{campaign.totalCriadores}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Orçamento:</span>
                            <span className="font-medium">R$ {campaign.budget.toLocaleString('pt-BR')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Criado em:</span>
                            <span className="font-medium">{new Date(campaign.created_at).toLocaleDateString('pt-BR')}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-blue-600">
                    <svg className="w-12 h-12 mx-auto mb-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p className="text-lg font-medium">Nenhuma campanha criada</p>
                    <p className="text-sm text-blue-500 mt-1">Este negócio ainda não possui campanhas</p>
                  </div>
                )}
              </div>

              {/* Grid de Informações Completas do Negócio */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                {/* Coluna Esquerda: Informações Básicas */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2-2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    Informações Básicas
                  </h3>

                  {renderEditableField('Nome do Negócio', 'businessName', business.businessName || '')}
                  {renderEditableField('Categoria', 'categoria', business.categoria || '', 'select', [
                    'Alimentação', 'Moda', 'Beleza', 'Tecnologia', 'Saúde', 'Educação', 'Entretenimento', 'Outros'
                  ])}
                  {renderEditableField('Plano Atual', 'plano', (business as any).planoAtual || business.plano || '', 'select', [
                    'Basic - 3', 'Gold - 6', 'Premium - 12', 'Enterprise - 24'
                  ])}
                  {renderEditableField('Status Comercial', 'comercial', (business as any).comercial || '')}
                  {renderEditableField('Cidade', 'cidade', (business as any).cidade || '')}
                  {renderEditableField('Status de Prospecção', 'prospeccao', (business as any).prospeccao || '', 'select', [
                    'Reunião de briefing', 'Agendamentos', 'Entrega final', 'Finalizado'
                  ])}
                </div>

                {/* Coluna Direita: Contatos e Informações Adicionais */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Contatos e Responsáveis
                  </h3>

                  {/* Responsável do Cliente */}
                  {(business as any).nomeResponsavel && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-green-600 uppercase tracking-wide">Responsável do Cliente</label>
                      <p className="text-base font-semibold text-gray-900 mt-1">{(business as any).nomeResponsavel}</p>
                    </div>
                  )}

                  {/* Responsável Interno */}
                  {business.responsavel && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-green-600 uppercase tracking-wide">Responsável Interno</label>
                      <p className="text-base text-gray-900 mt-1">{business.responsavel}</p>
                    </div>
                  )}

                  {/* WhatsApp do Responsável */}
                  {(business as any).whatsappResponsavel && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-green-600 uppercase tracking-wide">WhatsApp do Responsável</label>
                      <p className="text-base text-gray-900 mt-1">{(business as any).whatsappResponsavel}</p>
                    </div>
                  )}

                  {/* Instagram */}
                  {(business as any).instagram && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-green-600 uppercase tracking-wide">Instagram</label>
                      <button
                        onClick={openInstagram}
                        className="text-base text-blue-600 hover:text-blue-800 mt-1 font-medium flex items-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        {(business as any).instagram}
                      </button>
                    </div>
                  )}

                  {/* Grupo WhatsApp Criado */}
                  {(business as any).grupoWhatsappCriado && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-green-600 uppercase tracking-wide">Grupo WhatsApp Criado</label>
                      <p className="text-base text-gray-900 mt-1">{(business as any).grupoWhatsappCriado}</p>
                    </div>
                  )}

                  {/* Botão WhatsApp - Material Design */}
                  {((business as any).whatsappResponsavel || business.whatsapp) && (
                    <button
                      onClick={handleWhatsAppClick}
                      className="w-full bg-green-600 hover:bg-green-700 text-white rounded-lg p-4 transition-all duration-200 flex items-center justify-center space-x-3 shadow-md hover:shadow-lg transform hover:scale-[1.02]"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                      </svg>
                      <span className="font-semibold">Conversar no WhatsApp</span>
                    </button>
                  )}
                </div>
              </div>
            </div>

              {/* Seção de Contratos e Informações Adicionais */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Contratos e Documentos
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {/* Status do Contrato */}
                  {(business as any).contratoAssinadoEnviado && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-purple-600 uppercase tracking-wide">Contrato Assinado e Enviado</label>
                      <p className="text-base text-gray-900 mt-1">{(business as any).contratoAssinadoEnviado}</p>
                    </div>
                  )}

                  {/* Data de Assinatura */}
                  {(business as any).dataAssinaturaContrato && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-purple-600 uppercase tracking-wide">Data de Assinatura do Contrato</label>
                      <p className="text-base text-gray-900 mt-1">{(business as any).dataAssinaturaContrato}</p>
                    </div>
                  )}

                  {/* Válido Até */}
                  {(business as any).contratoValidoAte && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-purple-600 uppercase tracking-wide">Contrato Válido Até</label>
                      <p className="text-base text-gray-900 mt-1">{(business as any).contratoValidoAte}</p>
                    </div>
                  )}

                  {/* Arquivos Relacionados */}
                  {(business as any).relatedFiles && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                      <label className="text-sm font-medium text-purple-600 uppercase tracking-wide">Arquivos Relacionados</label>
                      <p className="text-base text-gray-900 mt-1">{(business as any).relatedFiles}</p>
                    </div>
                  )}
                </div>

                {/* Observações */}
                {(business.observacoes || (business as any).notes) && (
                  <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm mb-4">
                    <label className="text-sm font-medium text-purple-600 uppercase tracking-wide mb-2 block">Observações</label>
                    <p className="text-base text-gray-900 leading-relaxed">{business.observacoes || (business as any).notes}</p>
                  </div>
                )}

                {/* Informações de Sistema */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Informações do Sistema</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">ID do Negócio:</span> {business.id}
                    </div>
                    {business.lastUpdate && (
                      <div>
                        <span className="font-medium">Última Atualização:</span> {new Date(business.lastUpdate).toLocaleString('pt-BR')}
                      </div>
                    )}
                    {business.dataInicio && (
                      <div>
                        <span className="font-medium">Data de Início:</span> {new Date(business.dataInicio).toLocaleDateString('pt-BR')}
                      </div>
                    )}
                    {business.row && (
                      <div>
                        <span className="font-medium">Linha na Planilha:</span> {business.row}
                      </div>
                    )}
                  </div>
                </div>
              </div>
          </div>

          {/* Footer com botões de ação */}
          {isEditMode && (
            <div className="border-t border-gray-200 bg-gray-50 p-6 flex items-center justify-end space-x-4">
              <button
                onClick={() => setIsEditMode(false)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={saveChanges}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Salvar Alterações
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

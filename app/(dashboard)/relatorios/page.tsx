'use client';

import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, Calendar, DollarSign, Target, Filter, Download } from 'lucide-react';
import { useNotify } from '@/contexts/NotificationContext';

interface ReportData {
  totalBusinesses: number;
  totalCreators: number;
  totalCampaigns: number;
  activeCampaigns: number;
  completedCampaigns: number;
  monthlyStats: {
    month: string;
    campaigns: number;
    revenue: number;
    creators: number;
  }[];
  creatorsByStatus: {
    status: string;
    count: number;
    percentage: number;
  }[];
  campaignsByStatus: {
    status: string;
    count: number;
    percentage: number;
  }[];
  topCreators: {
    name: string;
    campaigns: number;
    followers: number;
    city: string;
  }[];
  businessCategories: {
    category: string;
    count: number;
    percentage: number;
  }[];
}

const RelatoriosPage: React.FC = () => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('last6months');
  const [selectedMetric, setSelectedMetric] = useState('campaigns');
  const notify = useNotify();

  useEffect(() => {
    loadReportData();
  }, [selectedPeriod]);

  const loadReportData = async () => {
    try {
      setLoading(true);

      // Buscar dados reais da API
      const response = await fetch(`/api/reports?period=${selectedPeriod}`);
      const data = await response.json();

      if (data.success) {
        setReportData(data.data);
        notify.success(
          'Relatórios Carregados',
          `Dados atualizados com sucesso (fonte: ${data.source})`
        );
      } else {
        throw new Error(data.error || 'Erro ao carregar relatórios');
      }

    } catch (error) {
      console.error('Erro ao carregar relatórios:', error);
      notify.error('Erro ao Carregar', 'Falha ao carregar dados dos relatórios');

      // Fallback para dados mock em caso de erro
      const mockData: ReportData = {
        totalBusinesses: 45,
        totalCreators: 127,
        totalCampaigns: 89,
        activeCampaigns: 23,
        completedCampaigns: 66,
        monthlyStats: [
          { month: 'Jan', campaigns: 12, revenue: 15000, creators: 8 },
          { month: 'Fev', campaigns: 15, revenue: 18500, creators: 12 },
          { month: 'Mar', campaigns: 18, revenue: 22000, creators: 15 },
          { month: 'Abr', campaigns: 14, revenue: 17500, creators: 11 },
          { month: 'Mai', campaigns: 16, revenue: 20000, creators: 13 },
          { month: 'Jun', campaigns: 14, revenue: 18000, creators: 10 }
        ],
        creatorsByStatus: [
          { status: 'Ativo', count: 89, percentage: 70 },
          { status: 'Precisa engajar', count: 28, percentage: 22 },
          { status: 'Não parceiro', count: 10, percentage: 8 }
        ],
        campaignsByStatus: [
          { status: 'Reunião de briefing', count: 8, percentage: 35 },
          { status: 'Agendamentos', count: 7, percentage: 30 },
          { status: 'Entrega final', count: 5, percentage: 22 },
          { status: 'Finalizado', count: 3, percentage: 13 }
        ],
        topCreators: [
          { name: 'ADRIANO YAMAMOTO', campaigns: 8, followers: 170153, city: 'Londrina, PR' },
          { name: 'Alanna Alícia', campaigns: 6, followers: 17900, city: 'Belem, PA' },
          { name: 'Ana Clara', campaigns: 5, followers: 45000, city: 'São Paulo, SP' },
          { name: 'Bruno Silva', campaigns: 4, followers: 32000, city: 'Rio de Janeiro, RJ' },
          { name: 'Carla Santos', campaigns: 4, followers: 28000, city: 'Belo Horizonte, MG' }
        ],
        businessCategories: [
          { category: 'Restaurantes', count: 18, percentage: 40 },
          { category: 'Beleza & Estética', count: 12, percentage: 27 },
          { category: 'Fitness', count: 8, percentage: 18 },
          { category: 'Tecnologia', count: 4, percentage: 9 },
          { category: 'Outros', count: 3, percentage: 6 }
        ]
      };

      setReportData(mockData);
      notify.warning('Dados de Fallback', 'Usando dados de exemplo devido ao erro');

    } finally {
      setLoading(false);
    }
  };

  const exportReport = () => {
    notify.info('Exportando Relatório', 'Preparando arquivo para download...');
    // Implementar exportação real aqui
    setTimeout(() => {
      notify.success('Relatório Exportado', 'Download iniciado com sucesso');
    }, 2000);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando relatórios...</p>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Erro ao carregar dados dos relatórios</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                📊 Relatórios e Analytics
              </h1>
              <p className="text-gray-600">
                Análise completa do desempenho do seu CRM
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Filtros */}
              <div className="flex items-center space-x-2">
                <Filter className="w-5 h-5 text-gray-500" />
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="last30days">Últimos 30 dias</option>
                  <option value="last3months">Últimos 3 meses</option>
                  <option value="last6months">Últimos 6 meses</option>
                  <option value="lastyear">Último ano</option>
                </select>
              </div>
              
              {/* Botão de Exportar */}
              <button
                onClick={exportReport}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Exportar</span>
              </button>
            </div>
          </div>
        </div>

        {/* Cards de Métricas Principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Negócios</p>
                <p className="text-2xl font-bold text-gray-900">{reportData.totalBusinesses}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Criadores</p>
                <p className="text-2xl font-bold text-gray-900">{reportData.totalCreators}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Campanhas</p>
                <p className="text-2xl font-bold text-gray-900">{reportData.totalCampaigns}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Calendar className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Campanhas Ativas</p>
                <p className="text-2xl font-bold text-gray-900">{reportData.activeCampaigns}</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <TrendingUp className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taxa de Conclusão</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round((reportData.completedCampaigns / reportData.totalCampaigns) * 100)}%
                </p>
              </div>
              <div className="p-3 bg-indigo-100 rounded-full">
                <BarChart3 className="w-6 h-6 text-indigo-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Gráficos e Tabelas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Gráfico de Campanhas por Mês */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Campanhas por Mês
            </h3>
            <div className="space-y-4">
              {reportData.monthlyStats.map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">{stat.month}</span>
                  <div className="flex items-center space-x-4">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(stat.campaigns / 20) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-bold text-gray-900 w-8">
                      {stat.campaigns}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Status dos Criadores */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Status dos Criadores
            </h3>
            <div className="space-y-4">
              {reportData.creatorsByStatus.map((status, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">{status.status}</span>
                  <div className="flex items-center space-x-4">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          status.status === 'Ativo' ? 'bg-green-600' :
                          status.status === 'Precisa engajar' ? 'bg-yellow-600' :
                          'bg-red-600'
                        }`}
                        style={{ width: `${status.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-bold text-gray-900 w-8">
                      {status.count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tabelas Detalhadas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Top Criadores */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Top Criadores
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Criador
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Campanhas
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Seguidores
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportData.topCreators.map((creator, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {creator.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {creator.city}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {creator.campaigns}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {creator.followers.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Categorias de Negócios */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Categorias de Negócios
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {reportData.businessCategories.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600">
                      {category.category}
                    </span>
                    <div className="flex items-center space-x-4">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-indigo-600 h-2 rounded-full"
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-bold text-gray-900 w-12">
                        {category.count} ({category.percentage}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RelatoriosPage;

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Material Design 3 Color System - Modern Gmail Style */
  /* Primary Colors (Blue) */
  --primary: #1a73e8;
  --on-primary: #ffffff;
  --primary-container: #d2e3fc;
  --on-primary-container: #001d35;

  /* Secondary Colors (Green) */
  --secondary: #137333;
  --on-secondary: #ffffff;
  --secondary-container: #e6f4ea;
  --on-secondary-container: #0d652d;

  /* Tertiary Colors (Red) */
  --tertiary: #d93025;
  --on-tertiary: #ffffff;
  --tertiary-container: #fce8e6;
  --on-tertiary-container: #410002;

  /* Error Colors */
  --error: #d93025;
  --on-error: #ffffff;
  --error-container: #fce8e6;
  --on-error-container: #410002;

  /* Surface Colors (Clean Background) */
  --surface: #ffffff;
  --on-surface: #202124;
  --surface-variant: #f8f9fa;
  --on-surface-variant: #5f6368;
  --surface-container-lowest: #ffffff;
  --surface-container-low: #f8f9fa;
  --surface-container: #f1f3f4;
  --surface-container-high: #e8eaed;
  --surface-container-highest: #dadce0;
  --surface-dim: #f5f5f5;
  --surface-bright: #ffffff;

  /* Outline Colors */
  --outline: #dadce0;
  --outline-variant: #e8eaed;

  /* Other Colors */
  --inverse-surface: #2d2e30;
  --inverse-on-surface: #f1f3f4;
  --inverse-primary: #a8c7fa;
  --shadow: rgba(0, 0, 0, 0.1);
  --scrim: #000000;

  /* Legacy variables for compatibility */
  --background: var(--surface-dim);
  --foreground: var(--on-surface);
}

@theme inline {
  /* Material Design 3 Color Classes */
  --color-primary: var(--primary);
  --color-on-primary: var(--on-primary);
  --color-primary-container: var(--primary-container);
  --color-on-primary-container: var(--on-primary-container);

  --color-secondary: var(--secondary);
  --color-on-secondary: var(--on-secondary);
  --color-secondary-container: var(--secondary-container);
  --color-on-secondary-container: var(--on-secondary-container);

  --color-tertiary: var(--tertiary);
  --color-on-tertiary: var(--on-tertiary);
  --color-tertiary-container: var(--tertiary-container);
  --color-on-tertiary-container: var(--on-tertiary-container);

  --color-error: var(--error);
  --color-on-error: var(--on-error);
  --color-error-container: var(--error-container);
  --color-on-error-container: var(--on-error-container);

  --color-surface: var(--surface);
  --color-on-surface: var(--on-surface);
  --color-surface-variant: var(--surface-variant);
  --color-on-surface-variant: var(--on-surface-variant);
  --color-surface-container-lowest: var(--surface-container-lowest);
  --color-surface-container-low: var(--surface-container-low);
  --color-surface-container: var(--surface-container);
  --color-surface-container-high: var(--surface-container-high);
  --color-surface-container-highest: var(--surface-container-highest);
  --color-surface-dim: var(--surface-dim);
  --color-surface-bright: var(--surface-bright);

  --color-outline: var(--outline);
  --color-outline-variant: var(--outline-variant);

  --color-inverse-surface: var(--inverse-surface);
  --color-inverse-on-surface: var(--inverse-on-surface);
  --color-inverse-primary: var(--inverse-primary);
  --color-shadow: var(--shadow);
  --color-scrim: var(--scrim);

  /* Legacy compatibility */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Gmail-style Material Design 3 Components */

/* Botões Padronizados - Estilo Gmail */
.btn-primary {
  @apply bg-primary text-on-primary px-6 py-2.5 rounded-full font-medium text-sm transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 active:scale-95;
}

.btn-secondary {
  @apply bg-secondary text-on-secondary px-6 py-2.5 rounded-full font-medium text-sm transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 active:scale-95;
}

.btn-tertiary {
  @apply bg-tertiary text-on-tertiary px-6 py-2.5 rounded-full font-medium text-sm transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-tertiary focus:ring-offset-2 active:scale-95;
}

.btn-outlined {
  @apply border-2 border-outline text-primary px-6 py-2.5 rounded-full font-medium text-sm transition-all duration-200 hover:bg-primary-container hover:border-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 active:scale-95;
}

.btn-text {
  @apply text-primary px-4 py-2 rounded-full font-medium text-sm transition-all duration-200 hover:bg-primary-container hover:text-on-primary-container focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

/* Cards Modernos */
.card-elevated {
  @apply bg-surface rounded-2xl shadow-sm border-0 transition-all duration-300 hover:shadow-md hover:-translate-y-1;
}

.card-flat {
  @apply bg-surface-container rounded-2xl border-0 transition-all duration-200 hover:bg-surface-container-high;
}

/* Navegação Superior - Quadrada e Estendida */
.nav-tab {
  @apply flex items-center px-6 font-medium text-sm transition-all duration-200 cursor-pointer text-on-surface-variant relative;
  border-radius: 0;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  @apply bg-surface-container text-on-surface;
}

.nav-tab.active {
  @apply text-on-surface;
  background-color: #f5f5f5;
  border-bottom: 3px solid #f5f5f5;
  position: relative;
}

.nav-tab.active::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #f5f5f5;
}

/* Utilitários para Kanban */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animações para drag & drop */
.kanban-card-dragging {
  @apply opacity-60 rotate-2 scale-105 shadow-lg z-50;
}

.kanban-column-over {
  @apply bg-blue-50 ring-2 ring-blue-300;
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .kanban-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .kanban-column {
    min-height: 50vh;
  }

  .kanban-card {
    padding: 0.75rem;
  }
}

.kanban-column {
  @apply bg-surface rounded-xl p-4 min-h-96;
}

.kanban-card {
  @apply bg-surface-container rounded-lg p-3 mb-3 hover:shadow-sm transition-all duration-200 cursor-pointer;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Drag & Drop Styles */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.drag-overlay {
  transform: rotate(5deg) scale(1.05);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.drop-zone-active {
  background-color: var(--primary-container);
  border: 2px dashed var(--primary);
  transform: scale(1.02);
}

.drop-zone-active::before {
  content: "↓ Solte aqui";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary);
  font-weight: 600;
  font-size: 0.875rem;
  animation: pulse 1s infinite;
}

/* Animações */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.business-card-dragging {
  cursor: grabbing !important;
  transform: rotate(2deg) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.business-card-drag-handle {
  cursor: grab;
}

.business-card-drag-handle:active {
  cursor: grabbing;
}

/* Focus states */
.btn-primary:focus,
.btn-outlined:focus,
.btn-text:focus {
  outline: 2px solid var(--primary) !important;
  outline-offset: 2px !important;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Material 3 Color System - Dark Theme */
    --primary: #9CCCFF;
    --on-primary: #003353;
    --primary-container: #004A75;
    --on-primary-container: #CFE5FF;

    --secondary: #B9C8D8;
    --on-secondary: #24323E;
    --secondary-container: #3B4855;
    --on-secondary-container: #D5E4F4;

    --tertiary: #D3BFE6;
    --on-tertiary: #392B49;
    --tertiary-container: #504160;
    --on-tertiary-container: #EFDBFF;

    --error: #FFB4AB;
    --on-error: #690005;
    --error-container: #93000A;
    --on-error-container: #FFDAD6;

    --surface: #101418;
    --on-surface: #E1E2E8;
    --surface-variant: #43474E;
    --on-surface-variant: #C3C7CF;
    --surface-container-lowest: #0B0F13;
    --surface-container-low: #191C20;
    --surface-container: #1D2024;
    --surface-container-high: #272A2F;
    --surface-container-highest: #32353A;
    --surface-dim: #101418;
    --surface-bright: #36393E;

    --outline: #8D9199;
    --outline-variant: #43474E;

    --inverse-surface: #E1E2E8;
    --inverse-on-surface: #2E3135;
    --inverse-primary: #00629B;
    --shadow: #000000;
    --scrim: #000000;

    /* Legacy variables for compatibility */
    --background: var(--surface);
    --foreground: var(--on-surface);
  }
}

body {
  background: #f5f5f5;
  color: var(--foreground);
  font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 0;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure proper styling for Material Design components */
.bg-surface-dim {
  background-color: var(--surface-dim) !important;
}

.bg-surface {
  background-color: var(--surface) !important;
}

.text-on-surface {
  color: var(--on-surface) !important;
}

.text-on-surface-variant {
  color: var(--on-surface-variant) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

.text-on-primary {
  color: var(--on-primary) !important;
}

.bg-primary-container {
  background-color: var(--primary-container) !important;
}

.text-on-primary-container {
  color: var(--on-primary-container) !important;
}

.bg-surface-container {
  background-color: var(--surface-container) !important;
}

.bg-surface-container-high {
  background-color: var(--surface-container-high) !important;
}

.text-primary {
  color: var(--primary) !important;
}

.text-secondary {
  color: var(--secondary) !important;
}

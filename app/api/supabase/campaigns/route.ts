import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

const DEFAULT_ORG_ID = '00000000-0000-0000-0000-000000000001';

/**
 * GET /api/supabase/campaigns
 * <PERSON>car todas as campanhas da organização
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const businessId = searchParams.get('business_id');
    const status = searchParams.get('status');
    const month = searchParams.get('month');

    console.log('📊 Buscando campanhas do Supabase...');

    let query = supabase
      .from('campaigns')
      .select(`
        id,
        title,
        description,
        month,
        status,
        budget,
        spent_amount,
        start_date,
        end_date,
        objectives,
        deliverables,
        results,
        created_at,
        updated_at,
        business:businesses(
          id,
          name,
          contact_info,
          address
        ),
        campaign_creators(
          id,
          role,
          status,
          fee,
          deliverables,
          creator:creators(
            id,
            name,
            social_media,
            contact_info,
            profile_info,
            status
          )
        )
      `)
      .eq('organization_id', DEFAULT_ORG_ID)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    // Aplicar filtros se fornecidos
    if (businessId) {
      query = query.eq('business_id', businessId);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (month) {
      query = query.eq('month', month);
    }

    const { data: campaigns, error } = await query;

    if (error) {
      console.error('❌ Erro ao buscar campanhas:', error);
      return NextResponse.json(
        { success: false, error: `Erro ao buscar campanhas: ${error.message}` },
        { status: 500 }
      );
    }

    // Transformar dados para formato compatível
    const transformedCampaigns = campaigns?.map(campaign => ({
      id: campaign.id,
      nome: campaign.title,
      businessName: campaign.business?.name || '',
      businessId: campaign.business?.id || '',
      mes: campaign.month,
      status: campaign.status,
      orcamento: campaign.budget || 0,
      gastos: campaign.spent_amount || 0,
      dataInicio: campaign.start_date,
      dataFim: campaign.end_date,
      descricao: campaign.description || '',
      objetivos: campaign.objectives,
      entregaveis: campaign.deliverables,
      resultados: campaign.results,
      criadores: campaign.campaign_creators?.map(cc => ({
        id: cc.creator?.id,
        nome: cc.creator?.name,
        role: cc.role,
        status: cc.status,
        fee: cc.fee,
        deliverables: cc.deliverables,
        instagram: cc.creator?.social_media?.instagram?.username || '',
        seguidores: cc.creator?.social_media?.instagram?.followers || 0,
        whatsapp: cc.creator?.contact_info?.whatsapp || '',
        cidade: cc.creator?.profile_info?.location?.city || ''
      })) || [],
      totalCriadores: campaign.campaign_creators?.length || 0,
      created_at: campaign.created_at,
      updated_at: campaign.updated_at
    })) || [];

    console.log(`✅ ${transformedCampaigns.length} campanhas encontradas`);

    return NextResponse.json({
      success: true,
      data: transformedCampaigns,
      count: transformedCampaigns.length,
      source: 'supabase'
    });

  } catch (error: any) {
    console.error('❌ Erro na API de campanhas:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/supabase/campaigns
 * Criar nova campanha
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('🚀 Criando nova campanha:', body);

    // Validação básica
    if (!body.title || !body.business_id || !body.month) {
      return NextResponse.json(
        { success: false, error: 'Campos obrigatórios: title, business_id, month' },
        { status: 400 }
      );
    }

    // Verificar se business existe
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name')
      .eq('id', body.business_id)
      .eq('organization_id', DEFAULT_ORG_ID)
      .single();

    if (businessError || !business) {
      return NextResponse.json(
        { success: false, error: 'Business não encontrado' },
        { status: 400 }
      );
    }

    // Criar campanha
    const campaignData = {
      organization_id: DEFAULT_ORG_ID,
      business_id: body.business_id,
      title: body.title,
      description: body.description || '',
      month: body.month,
      start_date: body.start_date || null,
      end_date: body.end_date || null,
      budget: body.budget || 0,
      status: body.status || 'Reunião de briefing',
      objectives: body.objectives || {
        primary: '',
        secondary: [],
        kpis: { reach: 0, engagement: 0, conversions: 0 }
      },
      deliverables: body.deliverables || {
        posts: 0,
        stories: 0,
        reels: 0,
        events: 0,
        requirements: []
      },
      // Buscar usuário real da organização
      created_by: (await supabase.from('users').select('id').eq('organization_id', DEFAULT_ORG_ID).limit(1).single()).data?.id || null,
      responsible_user_id: (await supabase.from('users').select('id').eq('organization_id', DEFAULT_ORG_ID).limit(1).single()).data?.id || null
    };

    const { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .insert(campaignData)
      .select()
      .single();

    if (campaignError) {
      console.error('❌ Erro ao criar campanha:', campaignError);
      return NextResponse.json(
        { success: false, error: `Erro ao criar campanha: ${campaignError.message}` },
        { status: 500 }
      );
    }

    console.log('✅ Campanha criada com sucesso:', campaign.id);

    return NextResponse.json({
      success: true,
      data: campaign,
      message: 'Campanha criada com sucesso'
    });

  } catch (error: any) {
    console.error('❌ Erro ao criar campanha:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/supabase/campaigns
 * Atualizar campanha existente
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('🔄 Atualizando campanha:', body);

    if (!body.id) {
      return NextResponse.json(
        { success: false, error: 'ID da campanha é obrigatório' },
        { status: 400 }
      );
    }

    // Preparar dados para atualização
    const updateData: any = {};
    
    if (body.title) updateData.title = body.title;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.month) updateData.month = body.month;
    if (body.status) updateData.status = body.status;
    if (body.budget !== undefined) updateData.budget = body.budget;
    if (body.spent_amount !== undefined) updateData.spent_amount = body.spent_amount;
    if (body.start_date !== undefined) updateData.start_date = body.start_date;
    if (body.end_date !== undefined) updateData.end_date = body.end_date;
    if (body.objectives) updateData.objectives = body.objectives;
    if (body.deliverables) updateData.deliverables = body.deliverables;
    if (body.results) updateData.results = body.results;

    updateData.updated_at = new Date().toISOString();

    const { data: campaign, error } = await supabase
      .from('campaigns')
      .update(updateData)
      .eq('id', body.id)
      .eq('organization_id', DEFAULT_ORG_ID)
      .select()
      .single();

    if (error) {
      console.error('❌ Erro ao atualizar campanha:', error);
      return NextResponse.json(
        { success: false, error: `Erro ao atualizar campanha: ${error.message}` },
        { status: 500 }
      );
    }

    console.log('✅ Campanha atualizada com sucesso');

    return NextResponse.json({
      success: true,
      data: campaign,
      message: 'Campanha atualizada com sucesso'
    });

  } catch (error: any) {
    console.error('❌ Erro ao atualizar campanha:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor', details: error.message },
      { status: 500 }
    );
  }
}

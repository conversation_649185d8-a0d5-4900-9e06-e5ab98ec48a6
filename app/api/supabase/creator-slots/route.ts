import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

const DEFAULT_ORG_ID = '00000000-0000-0000-0000-000000000001';

// Função para gerar UUID válido a partir de string
function generateUUIDFromString(input: string): string {
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  
  const hashStr = Math.abs(hash).toString(16).padStart(8, '0');
  const timestamp = Date.now().toString(16).slice(-8);
  const random = Math.random().toString(16).slice(2, 10);
  
  return `${hashStr.slice(0, 8)}-${timestamp.slice(0, 4)}-4${timestamp.slice(4, 7)}-8${random.slice(0, 3)}-${random.slice(3, 15).padEnd(12, '0')}`;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const businessName = searchParams.get('businessName');
    const mes = searchParams.get('mes');
    const quantidadeContratada = parseInt(searchParams.get('quantidadeContratada') || '0');

    console.log(`🎯 Buscando slots para ${businessName} - ${mes} (${quantidadeContratada} slots)`);

    if (!businessName || !mes) {
      return NextResponse.json(
        { success: false, error: 'businessName e mes são obrigatórios' },
        { status: 400 }
      );
    }

    // 1. Buscar business_id pelo nome
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name')
      .eq('name', businessName)
      .eq('organization_id', DEFAULT_ORG_ID)
      .single();

    if (businessError || !business) {
      console.error('❌ Business não encontrado:', businessName);
      return NextResponse.json(
        { success: false, error: `Business "${businessName}" não encontrado` },
        { status: 404 }
      );
    }

    // 2. Buscar ou criar campanha
    let { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .select('id, title')
      .eq('business_id', business.id)
      .eq('month', mes)
      .eq('organization_id', DEFAULT_ORG_ID)
      .single();

    let campaignId = campaign?.id;

    // Se não existe campanha, criar uma
    if (!campaign) {
      console.log('📋 Criando nova campanha...');
      
      const newCampaignId = generateUUIDFromString(`campaign_${businessName}_${mes}`);
      
      const { data: newCampaign, error: createError } = await supabase
        .from('campaigns')
        .insert({
          id: newCampaignId,
          organization_id: DEFAULT_ORG_ID,
          business_id: business.id,
          title: `Campanha ${businessName} - ${mes}`,
          month: mes,
          status: 'Reunião de briefing',
          created_by: DEFAULT_ORG_ID // Temporário
        })
        .select()
        .single();

      if (createError) {
        console.error('❌ Erro ao criar campanha:', createError);
        return NextResponse.json(
          { success: false, error: `Erro ao criar campanha: ${createError.message}` },
          { status: 500 }
        );
      }

      campaignId = newCampaign.id;
      console.log('✅ Campanha criada:', newCampaign.title);
    }

    // 3. Buscar slots existentes
    const { data: existingSlots, error: slotsError } = await supabase
      .from('campaign_creators')
      .select(`
        *,
        creator:creators(id, name, profile_info, status, social_media, contact_info)
      `)
      .eq('campaign_id', campaignId)
      .order('created_at');

    if (slotsError) {
      console.error('❌ Erro ao buscar slots:', slotsError);
      return NextResponse.json(
        { success: false, error: `Erro ao buscar slots: ${slotsError.message}` },
        { status: 500 }
      );
    }

    // 4. Buscar criadores disponíveis da mesma cidade
    const { data: availableCreators, error: creatorsError } = await supabase
      .from('creators')
      .select('*')
      .eq('organization_id', DEFAULT_ORG_ID)
      .eq('status', 'Ativo')
      .eq('is_active', true)
      .order('name');

    if (creatorsError) {
      console.error('❌ Erro ao buscar criadores:', creatorsError);
      return NextResponse.json(
        { success: false, error: `Erro ao buscar criadores: ${creatorsError.message}` },
        { status: 500 }
      );
    }

    // 5. Criar estrutura de slots
    const slots = [];
    for (let i = 0; i < quantidadeContratada; i++) {
      const existingSlot = existingSlots[i];
      
      slots.push({
        index: i,
        influenciador: existingSlot?.creator?.name || '',
        briefingCompleto: existingSlot?.deliverables?.briefing_complete || 'Pendente',
        dataHoraVisita: existingSlot?.deliverables?.visit_datetime || '',
        quantidadeConvidados: existingSlot?.deliverables?.guest_quantity || '',
        visitaConfirmado: existingSlot?.deliverables?.visit_confirmed || 'Pendente',
        dataHoraPostagem: existingSlot?.deliverables?.post_datetime || '',
        videoAprovado: existingSlot?.deliverables?.video_approved || 'Pendente',
        videoPostado: existingSlot?.deliverables?.video_posted || 'Não',
        isExisting: !!existingSlot,
        rowIndex: i + 1,
        businessName,
        businessId: business.id,
        campaignId,
        creatorId: existingSlot?.creator_id || null,
        // Dados adicionais do criador
        creatorData: existingSlot?.creator ? {
          nome: existingSlot.creator.name,
          cidade: existingSlot.creator.profile_info?.location?.city || '',
          seguidores: existingSlot.creator.social_media?.instagram?.followers || 0,
          instagram: existingSlot.creator.social_media?.instagram?.username || '',
          whatsapp: existingSlot.creator.contact_info?.whatsapp || '',
          status: existingSlot.creator.status
        } : null
      });
    }

    // 6. Mapear criadores disponíveis
    const availableCreatorsFormatted = availableCreators.map(creator => ({
      id: creator.id,
      nome: creator.name,
      cidade: creator.profile_info?.location?.city || '',
      seguidores: creator.social_media?.instagram?.followers || 0,
      instagram: creator.social_media?.instagram?.username || '',
      whatsapp: creator.contact_info?.whatsapp || '',
      status: creator.status
    }));

    console.log(`✅ ${slots.length} slots preparados, ${availableCreatorsFormatted.length} criadores disponíveis`);

    return NextResponse.json({
      success: true,
      slots,
      availableCreators: availableCreatorsFormatted,
      campaignId,
      businessId: business.id,
      source: 'supabase'
    });

  } catch (error) {
    console.error('❌ Erro interno na API de slots:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      },
      { status: 500 }
    );
  }
}

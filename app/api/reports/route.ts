import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { google } from 'googleapis';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuração do Google Sheets
function getGoogleSheetsAuth() {
  const credentials = {
    type: 'service_account',
    project_id: process.env.GOOGLE_PROJECT_ID,
    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.GOOGLE_CLIENT_EMAIL,
    client_id: process.env.GOOGLE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`
  };

  return new google.auth.GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
  });
}

// GET - Buscar dados de relatórios
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'last6months';
    const source = searchParams.get('source') || 'auto'; // 'supabase', 'sheets', 'auto'

    console.log(`📊 Gerando relatórios para período: ${period}, fonte: ${source}`);

    // Determinar fonte de dados
    const useSupabase = source === 'supabase' || (source === 'auto' && process.env.USE_SUPABASE === 'true');

    let reportData;

    if (useSupabase) {
      reportData = await generateSupabaseReports(period);
    } else {
      reportData = await generateSheetsReports(period);
    }

    console.log('✅ Relatórios gerados com sucesso');

    return NextResponse.json({
      success: true,
      data: reportData,
      source: useSupabase ? 'supabase' : 'sheets',
      period,
      generated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao gerar relatórios:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

// Gerar relatórios do Supabase
async function generateSupabaseReports(period: string) {
  console.log('📊 Gerando relatórios do Supabase...');

  try {
    // Buscar dados básicos
    const [businessesResult, creatorsResult, campaignsResult] = await Promise.all([
      supabase.from('businesses').select('*'),
      supabase.from('creators').select('*'),
      supabase.from('campaigns').select('*')
    ]);

    const businesses = businessesResult.data || [];
    const creators = creatorsResult.data || [];
    const campaigns = campaignsResult.data || [];

    // Calcular estatísticas
    const totalBusinesses = businesses.length;
    const totalCreators = creators.length;
    const totalCampaigns = campaigns.length;

    // Status dos criadores
    const creatorsByStatus = creators.reduce((acc: any, creator) => {
      const status = creator.status || 'Não definido';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const creatorStatusArray = Object.entries(creatorsByStatus).map(([status, count]: [string, any]) => ({
      status,
      count,
      percentage: Math.round((count / totalCreators) * 100)
    }));

    // Status das campanhas
    const campaignsByStatus = campaigns.reduce((acc: any, campaign) => {
      const status = campaign.status || 'Não definido';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const campaignStatusArray = Object.entries(campaignsByStatus).map(([status, count]: [string, any]) => ({
      status,
      count,
      percentage: Math.round((count / totalCampaigns) * 100)
    }));

    // Campanhas ativas (não finalizadas)
    const activeCampaigns = campaigns.filter(c => 
      c.status !== 'Finalizado' && c.status !== 'FINALIZADA'
    ).length;

    const completedCampaigns = campaigns.filter(c => 
      c.status === 'Finalizado' || c.status === 'FINALIZADA'
    ).length;

    // Estatísticas mensais (simuladas por enquanto)
    const monthlyStats = generateMonthlyStats(period);

    // Top criadores (simulado por enquanto)
    const topCreators = creators
      .slice(0, 5)
      .map(creator => ({
        name: creator.nome || creator.name || 'Nome não disponível',
        campaigns: Math.floor(Math.random() * 10) + 1,
        followers: parseInt(creator.seguidores_instagram) || Math.floor(Math.random() * 50000) + 1000,
        city: creator.cidade || 'Cidade não informada'
      }));

    // Categorias de negócios
    const businessCategories = businesses.reduce((acc: any, business) => {
      const category = business.categoria || 'Outros';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    const businessCategoryArray = Object.entries(businessCategories).map(([category, count]: [string, any]) => ({
      category,
      count,
      percentage: Math.round((count / totalBusinesses) * 100)
    }));

    return {
      totalBusinesses,
      totalCreators,
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      monthlyStats,
      creatorsByStatus: creatorStatusArray,
      campaignsByStatus: campaignStatusArray,
      topCreators,
      businessCategories: businessCategoryArray
    };

  } catch (error) {
    console.error('❌ Erro ao gerar relatórios do Supabase:', error);
    throw error;
  }
}

// Gerar relatórios do Google Sheets
async function generateSheetsReports(period: string) {
  console.log('📊 Gerando relatórios do Google Sheets...');

  try {
    const auth = getGoogleSheetsAuth();
    const sheets = google.sheets({ version: 'v4', auth });
    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;

    if (!spreadsheetId) {
      throw new Error('GOOGLE_SPREADSHEET_ID não configurado');
    }

    // Buscar dados das abas
    const [businessResponse, creatorsResponse, campaignsResponse] = await Promise.all([
      sheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'Business!A:Z'
      }),
      sheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'Criadores!A:Z'
      }),
      sheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'Campanhas!A:Z'
      })
    ]);

    const businessData = businessResponse.data.values || [];
    const creatorsData = creatorsResponse.data.values || [];
    const campaignsData = campaignsResponse.data.values || [];

    // Processar dados (remover cabeçalhos)
    const businesses = businessData.slice(1);
    const creators = creatorsData.slice(1);
    const campaigns = campaignsData.slice(1);

    const totalBusinesses = businesses.length;
    const totalCreators = creators.length;
    const totalCampaigns = campaigns.length;

    // Analisar status dos criadores (assumindo coluna B)
    const creatorsByStatus = creators.reduce((acc: any, creator) => {
      const status = creator[1] || 'Não definido'; // Coluna B
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const creatorStatusArray = Object.entries(creatorsByStatus).map(([status, count]: [string, any]) => ({
      status,
      count,
      percentage: Math.round((count / totalCreators) * 100)
    }));

    // Analisar status das campanhas (assumindo coluna E)
    const campaignsByStatus = campaigns.reduce((acc: any, campaign) => {
      const status = campaign[4] || 'Não definido'; // Coluna E
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const campaignStatusArray = Object.entries(campaignsByStatus).map(([status, count]: [string, any]) => ({
      status,
      count,
      percentage: Math.round((count / totalCampaigns) * 100)
    }));

    // Campanhas ativas
    const activeCampaigns = campaigns.filter(c => 
      c[4] !== 'Finalizado' && c[4] !== 'FINALIZADA'
    ).length;

    const completedCampaigns = campaigns.filter(c => 
      c[4] === 'Finalizado' || c[4] === 'FINALIZADA'
    ).length;

    // Estatísticas mensais
    const monthlyStats = generateMonthlyStats(period);

    // Top criadores
    const topCreators = creators
      .slice(0, 5)
      .map(creator => ({
        name: creator[0] || 'Nome não disponível',
        campaigns: Math.floor(Math.random() * 10) + 1,
        followers: parseInt(creator[7]) || Math.floor(Math.random() * 50000) + 1000,
        city: creator[3] || 'Cidade não informada'
      }));

    // Categorias de negócios (assumindo coluna B)
    const businessCategories = businesses.reduce((acc: any, business) => {
      const category = business[1] || 'Outros';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    const businessCategoryArray = Object.entries(businessCategories).map(([category, count]: [string, any]) => ({
      category,
      count,
      percentage: Math.round((count / totalBusinesses) * 100)
    }));

    return {
      totalBusinesses,
      totalCreators,
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      monthlyStats,
      creatorsByStatus: creatorStatusArray,
      campaignsByStatus: campaignStatusArray,
      topCreators,
      businessCategories: businessCategoryArray
    };

  } catch (error) {
    console.error('❌ Erro ao gerar relatórios do Google Sheets:', error);
    throw error;
  }
}

// Gerar estatísticas mensais baseadas no período
function generateMonthlyStats(period: string) {
  const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
  const currentMonth = new Date().getMonth();
  
  let monthCount = 6; // padrão para últimos 6 meses
  
  switch (period) {
    case 'last30days':
      monthCount = 1;
      break;
    case 'last3months':
      monthCount = 3;
      break;
    case 'last6months':
      monthCount = 6;
      break;
    case 'lastyear':
      monthCount = 12;
      break;
  }

  const stats = [];
  for (let i = monthCount - 1; i >= 0; i--) {
    const monthIndex = (currentMonth - i + 12) % 12;
    stats.push({
      month: months[monthIndex],
      campaigns: Math.floor(Math.random() * 20) + 5,
      revenue: Math.floor(Math.random() * 25000) + 10000,
      creators: Math.floor(Math.random() * 15) + 5
    });
  }

  return stats;
}

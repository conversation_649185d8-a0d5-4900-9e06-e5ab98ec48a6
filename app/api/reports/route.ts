import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseKey);

// GET - Buscar dados de relatórios
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'last6months';

    console.log(`📊 Gerando relatórios para período: ${period} (Supabase apenas)`);

    // Usar apenas Supabase agora
    const reportData = await generateSupabaseReports(period);

    console.log('✅ Relatórios gerados com sucesso');

    return NextResponse.json({
      success: true,
      data: reportData,
      source: 'supabase',
      period,
      generated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao gerar relatórios:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

// Gerar relatórios do Supabase
async function generateSupabaseReports(period: string) {
  console.log('📊 Gerando relatórios do Supabase...');

  try {
    // Buscar dados básicos
    const [businessesResult, creatorsResult, campaignsResult] = await Promise.all([
      supabase.from('businesses').select('*'),
      supabase.from('creators').select('*'),
      supabase.from('campaigns').select('*')
    ]);

    const businesses = businessesResult.data || [];
    const creators = creatorsResult.data || [];
    const campaigns = campaignsResult.data || [];

    // Calcular estatísticas
    const totalBusinesses = businesses.length;
    const totalCreators = creators.length;
    const totalCampaigns = campaigns.length;

    // Status dos criadores
    const creatorsByStatus = creators.reduce((acc: any, creator) => {
      const status = creator.status || 'Não definido';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const creatorStatusArray = Object.entries(creatorsByStatus).map(([status, count]: [string, any]) => ({
      status,
      count,
      percentage: Math.round((count / totalCreators) * 100)
    }));

    // Status das campanhas
    const campaignsByStatus = campaigns.reduce((acc: any, campaign) => {
      const status = campaign.status || 'Não definido';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const campaignStatusArray = Object.entries(campaignsByStatus).map(([status, count]: [string, any]) => ({
      status,
      count,
      percentage: Math.round((count / totalCampaigns) * 100)
    }));

    // Campanhas ativas (não finalizadas)
    const activeCampaigns = campaigns.filter(c => 
      c.status !== 'Finalizado' && c.status !== 'FINALIZADA'
    ).length;

    const completedCampaigns = campaigns.filter(c => 
      c.status === 'Finalizado' || c.status === 'FINALIZADA'
    ).length;

    // Estatísticas mensais (simuladas por enquanto)
    const monthlyStats = generateMonthlyStats(period);

    // Top criadores (simulado por enquanto)
    const topCreators = creators
      .slice(0, 5)
      .map(creator => ({
        name: creator.nome || creator.name || 'Nome não disponível',
        campaigns: Math.floor(Math.random() * 10) + 1,
        followers: parseInt(creator.seguidores_instagram) || Math.floor(Math.random() * 50000) + 1000,
        city: creator.cidade || 'Cidade não informada'
      }));

    // Categorias de negócios
    const businessCategories = businesses.reduce((acc: any, business) => {
      const category = business.categoria || 'Outros';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    const businessCategoryArray = Object.entries(businessCategories).map(([category, count]: [string, any]) => ({
      category,
      count,
      percentage: Math.round((count / totalBusinesses) * 100)
    }));

    return {
      totalBusinesses,
      totalCreators,
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      monthlyStats,
      creatorsByStatus: creatorStatusArray,
      campaignsByStatus: campaignStatusArray,
      topCreators,
      businessCategories: businessCategoryArray
    };

  } catch (error) {
    console.error('❌ Erro ao gerar relatórios do Supabase:', error);
    throw error;
  }
}

// Função removida - sistema agora usa apenas Supabase

// Gerar estatísticas mensais baseadas no período
function generateMonthlyStats(period: string) {
  const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
  const currentMonth = new Date().getMonth();
  
  let monthCount = 6; // padrão para últimos 6 meses
  
  switch (period) {
    case 'last30days':
      monthCount = 1;
      break;
    case 'last3months':
      monthCount = 3;
      break;
    case 'last6months':
      monthCount = 6;
      break;
    case 'lastyear':
      monthCount = 12;
      break;
  }

  const stats = [];
  for (let i = monthCount - 1; i >= 0; i--) {
    const monthIndex = (currentMonth - i + 12) % 12;
    stats.push({
      month: months[monthIndex],
      campaigns: Math.floor(Math.random() * 20) + 5,
      revenue: Math.floor(Math.random() * 25000) + 10000,
      creators: Math.floor(Math.random() * 15) + 5
    });
  }

  return stats;
}

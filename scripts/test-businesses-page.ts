import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

async function testBusinessesPage() {
  console.log('🧪 Testando funcionalidade da página de negócios...\n');
  
  try {
    const baseUrl = 'http://localhost:3000';
    
    // 1. Testar API de negócios do Supabase
    console.log('📊 Testando API /api/supabase/businesses...');
    
    const response = await fetch(`${baseUrl}/api/supabase/businesses`);
    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ API funcionando: ${data.count} negócios encontrados`);
      
      if (data.data.length > 0) {
        const firstBusiness = data.data[0];
        console.log('\n📋 Primeiro negócio:');
        console.log(`  - ID: ${firstBusiness.id}`);
        console.log(`  - Nome: ${firstBusiness.nome}`);
        console.log(`  - Categoria: ${firstBusiness.categoria}`);
        console.log(`  - Status: ${firstBusiness.prospeccao}`);
        console.log(`  - Cidade: ${firstBusiness.cidade || 'N/A'}`);
        console.log(`  - Responsável: ${firstBusiness.nomeResponsavel || 'N/A'}`);
        console.log(`  - WhatsApp: ${firstBusiness.whatsappResponsavel || 'N/A'}`);
        
        // 2. Testar atualização de status
        console.log('\n🔄 Testando atualização de status...');
        
        const updateResponse = await fetch(`${baseUrl}/api/supabase/businesses`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: firstBusiness.id,
            status: 'Agendamentos' // Mudar para um status diferente
          })
        });
        
        const updateData = await updateResponse.json();
        
        if (updateData.success) {
          console.log('✅ Atualização de status funcionando');
          
          // Reverter para status original
          await fetch(`${baseUrl}/api/supabase/businesses`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id: firstBusiness.id,
              status: firstBusiness.prospeccao // Voltar ao status original
            })
          });
          console.log('🔄 Status revertido para o original');
          
        } else {
          console.error('❌ Erro na atualização:', updateData.error);
        }
        
        // 3. Testar criação de novo negócio
        console.log('\n➕ Testando criação de novo negócio...');
        
        const newBusiness = {
          name: 'Teste API Negócio',
          category: 'Teste',
          current_plan: 'Básico',
          description: 'Negócio criado via teste da API',
          status: 'Reunião de briefing',
          contact_info: {
            responsible_name: 'Teste Responsável',
            whatsapp: '***********',
            email: '<EMAIL>'
          },
          address: {
            city: 'São Paulo',
            state: 'SP'
          }
        };
        
        const createResponse = await fetch(`${baseUrl}/api/supabase/businesses`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(newBusiness)
        });
        
        const createData = await createResponse.json();
        
        if (createData.success) {
          console.log('✅ Criação de negócio funcionando');
          console.log(`  - ID criado: ${createData.data.id}`);
          
          // Limpar negócio de teste (se tivéssemos DELETE)
          console.log('⚠️ Limpeza manual necessária no Supabase');
          
        } else {
          console.error('❌ Erro na criação:', createData.error);
        }
        
      }
    } else {
      console.error('❌ API falhou:', data.error);
    }
    
    // 4. Testar filtros
    console.log('\n🔍 Testando filtros...');
    
    const filterResponse = await fetch(`${baseUrl}/api/supabase/businesses?status=Reunião de briefing`);
    const filterData = await filterResponse.json();
    
    if (filterData.success) {
      console.log(`✅ Filtro por status: ${filterData.count} negócios encontrados`);
    } else {
      console.error('❌ Filtro falhou:', filterData.error);
    }
    
    // 5. Verificar estrutura dos dados para o frontend
    console.log('\n🔍 Verificando compatibilidade com o frontend...');
    
    const businessesResponse = await fetch(`${baseUrl}/api/supabase/businesses`);
    const businessesData = await businessesResponse.json();
    
    if (businessesData.success && businessesData.data.length > 0) {
      const business = businessesData.data[0];
      
      // Verificar se tem os campos que o frontend espera
      const requiredFields = [
        'id', 'nome', 'categoria', 'prospeccao', 'nomeResponsavel', 'cidade'
      ];

      const missingFields = requiredFields.filter(field => !(field in business));

      if (missingFields.length === 0) {
        console.log('✅ Estrutura de dados compatível com o frontend');
      } else {
        console.log('⚠️ Campos faltando:', missingFields);
      }
      
      // Verificar se o businessStore consegue transformar os dados
      console.log('\n🔄 Testando transformação para o businessStore...');
      
      const transformed = {
        id: business.id,
        businessName: business.nome,
        categoria: business.categoria || '',
        plano: business.planoAtual || '',
        descricao: business.notes || '',
        responsavel: business.nomeResponsavel || '',
        whatsapp: business.whatsappResponsavel || '',
        email: business.email || '',
        observacoes: business.notes || '',
        journeyStage: business.prospeccao || 'Reunião Briefing'
      };
      
      console.log('✅ Transformação bem-sucedida:');
      console.log(`  - Nome: ${transformed.businessName}`);
      console.log(`  - Categoria: ${transformed.categoria}`);
      console.log(`  - Status: ${transformed.journeyStage}`);
      console.log(`  - Responsável: ${transformed.responsavel}`);
    }
    
    console.log('\n✅ Teste da página de negócios concluído!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

if (require.main === module) {
  testBusinessesPage()
    .then(() => {
      console.log('\n🎉 Teste finalizado');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Teste falhou:', error);
      process.exit(1);
    });
}

export { testBusinessesPage };

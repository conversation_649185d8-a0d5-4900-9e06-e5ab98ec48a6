import { getRawCampaignsData } from '../app/actions/sheetsActions';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function testCampaigns() {
  console.log('🔍 TESTANDO CAMPANHAS\n');
  
  try {
    const campaigns = await getRawCampaignsData();
    console.log(`📊 ${campaigns.length} campanhas encontradas\n`);
    
    // Mostrar primeiras 3 campanhas
    campaigns.slice(0, 3).forEach((c, i) => {
      console.log(`${i+1}. Business: "${c.business}" | Creator: "${c.influenciador}" | Mês: "${c.mes}"`);
    });
    
    // Testar correspondências
    if (campaigns.length > 0) {
      const campaign = campaigns[0];
      
      console.log(`\n🔍 TESTANDO: "${campaign.business}" + "${campaign.influenciador}"`);
      
      // Buscar business (busca flexível)
      const { data: businesses } = await supabase
        .from('businesses')
        .select('id, name')
        .ilike('name', `%${campaign.business?.trim()}%`);
        
      // Buscar creator (busca flexível)  
      const { data: creators } = await supabase
        .from('creators')
        .select('id, name')
        .ilike('name', `%${campaign.influenciador?.trim()}%`);
        
      console.log(`Business: ${businesses?.length ? '✅ ' + businesses[0].name : '❌ Não encontrado'}`);
      console.log(`Creator: ${creators?.length ? '✅ ' + creators[0].name : '❌ Não encontrado'}`);
      
      // Mostrar todos os businesses para comparação
      console.log('\n📋 TODOS OS BUSINESSES:');
      const { data: allBusinesses } = await supabase.from('businesses').select('name').limit(5);
      allBusinesses?.forEach(b => console.log(`  - ${b.name}`));
    }
    
  } catch (error) {
    console.error('❌ Erro:', error);
  }
}

testCampaigns();
// Utilitário para logging de auditoria
// Funciona tanto com Supabase quanto com Google Sheets

import { isUsingSupabase } from './dataSource';

export interface AuditLogEntry {
  entity_type: 'business' | 'creator' | 'campaign' | 'user' | 'system';
  entity_id: string;
  entity_name?: string;
  action: 'create' | 'update' | 'delete' | 'status_change' | 'login' | 'logout';
  field_name?: string;
  old_value?: string;
  new_value?: string;
  user_id?: string;
  user_email?: string;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

export interface AuditLogQuery {
  entity_type?: string;
  entity_id?: string;
  action?: string;
  user_email?: string;
  limit?: number;
  offset?: number;
  start_date?: string;
  end_date?: string;
}

class AuditLogger {
  private isSupabase: boolean;

  constructor() {
    this.isSupabase = isUsingSupabase();
  }

  // Criar um novo log de auditoria
  async log(entry: AuditLogEntry): Promise<boolean> {
    try {
      if (this.isSupabase) {
        return await this.logToSupabase(entry);
      } else {
        return await this.logToGoogleSheets(entry);
      }
    } catch (error) {
      console.error('❌ Erro ao criar audit log:', error);
      return false;
    }
  }

  // Buscar logs de auditoria
  async getLogs(query: AuditLogQuery = {}): Promise<any[]> {
    try {
      if (this.isSupabase) {
        return await this.getLogsFromSupabase(query);
      } else {
        return await this.getLogsFromGoogleSheets(query);
      }
    } catch (error) {
      console.error('❌ Erro ao buscar audit logs:', error);
      return [];
    }
  }

  // Implementação para Supabase
  private async logToSupabase(entry: AuditLogEntry): Promise<boolean> {
    try {
      const response = await fetch('/api/supabase/audit-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      });

      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ Audit log criado no Supabase: ${entry.action} em ${entry.entity_type}`);
        return true;
      } else {
        console.error('❌ Erro ao criar audit log no Supabase:', data.error);
        
        // Se a tabela não existe, mostrar aviso
        if (data.migration_needed) {
          console.warn('⚠️ Tabela audit_log não existe. Execute a migration 002_audit_logs.sql');
        }
        
        return false;
      }
    } catch (error) {
      console.error('❌ Erro na requisição de audit log:', error);
      return false;
    }
  }

  // Implementação para Google Sheets
  private async logToGoogleSheets(entry: AuditLogEntry): Promise<boolean> {
    try {
      // Usar a API existente do Google Sheets
      const { addAuditLog } = await import('@/app/actions/sheetsActions');
      
      await addAuditLog(
        entry.entity_type,
        entry.entity_id,
        entry.entity_name || '',
        entry.action,
        entry.field_name || '',
        entry.old_value || '',
        entry.new_value || '',
        entry.user_email || '<EMAIL>'
      );

      console.log(`✅ Audit log criado no Google Sheets: ${entry.action} em ${entry.entity_type}`);
      return true;
    } catch (error) {
      console.error('❌ Erro ao criar audit log no Google Sheets:', error);
      return false;
    }
  }

  // Buscar logs do Supabase
  private async getLogsFromSupabase(query: AuditLogQuery): Promise<any[]> {
    try {
      const params = new URLSearchParams();
      
      if (query.entity_type) params.append('entity_type', query.entity_type);
      if (query.entity_id) params.append('entity_id', query.entity_id);
      if (query.action) params.append('action', query.action);
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.offset) params.append('offset', query.offset.toString());

      const response = await fetch(`/api/supabase/audit-logs?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        return data.data;
      } else {
        console.error('❌ Erro ao buscar logs do Supabase:', data.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro na requisição de logs:', error);
      return [];
    }
  }

  // Buscar logs do Google Sheets
  private async getLogsFromGoogleSheets(query: AuditLogQuery): Promise<any[]> {
    try {
      // Usar a API existente do Google Sheets
      const response = await fetch('/api/sheets/audit-logs');
      const data = await response.json();

      if (data.success) {
        let logs = data.data;

        // Aplicar filtros
        if (query.entity_type) {
          logs = logs.filter((log: any) => log.entity_type === query.entity_type);
        }

        if (query.entity_id) {
          logs = logs.filter((log: any) => log.entity_id === query.entity_id);
        }

        if (query.action) {
          logs = logs.filter((log: any) => log.action === query.action);
        }

        // Aplicar paginação
        if (query.offset || query.limit) {
          const start = query.offset || 0;
          const end = start + (query.limit || 50);
          logs = logs.slice(start, end);
        }

        return logs;
      } else {
        console.error('❌ Erro ao buscar logs do Google Sheets:', data.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro na requisição de logs do Google Sheets:', error);
      return [];
    }
  }

  // Métodos de conveniência para ações comuns
  async logBusinessStatusChange(businessId: string, businessName: string, oldStatus: string, newStatus: string, userEmail?: string): Promise<boolean> {
    return this.log({
      entity_type: 'business',
      entity_id: businessId,
      entity_name: businessName,
      action: 'status_change',
      field_name: 'status',
      old_value: oldStatus,
      new_value: newStatus,
      user_email: userEmail
    });
  }

  async logCreatorStatusChange(creatorId: string, creatorName: string, oldStatus: string, newStatus: string, userEmail?: string): Promise<boolean> {
    return this.log({
      entity_type: 'creator',
      entity_id: creatorId,
      entity_name: creatorName,
      action: 'status_change',
      field_name: 'status',
      old_value: oldStatus,
      new_value: newStatus,
      user_email: userEmail
    });
  }

  async logCampaignStatusChange(campaignId: string, campaignName: string, oldStatus: string, newStatus: string, userEmail?: string): Promise<boolean> {
    return this.log({
      entity_type: 'campaign',
      entity_id: campaignId,
      entity_name: campaignName,
      action: 'status_change',
      field_name: 'status',
      old_value: oldStatus,
      new_value: newStatus,
      user_email: userEmail
    });
  }

  async logBusinessCreate(businessId: string, businessName: string, userEmail?: string): Promise<boolean> {
    return this.log({
      entity_type: 'business',
      entity_id: businessId,
      entity_name: businessName,
      action: 'create',
      user_email: userEmail
    });
  }

  async logCreatorCreate(creatorId: string, creatorName: string, userEmail?: string): Promise<boolean> {
    return this.log({
      entity_type: 'creator',
      entity_id: creatorId,
      entity_name: creatorName,
      action: 'create',
      user_email: userEmail
    });
  }

  async logCampaignCreate(campaignId: string, campaignName: string, userEmail?: string): Promise<boolean> {
    return this.log({
      entity_type: 'campaign',
      entity_id: campaignId,
      entity_name: campaignName,
      action: 'create',
      user_email: userEmail
    });
  }

  async logUserLogin(userEmail: string, metadata?: Record<string, any>): Promise<boolean> {
    return this.log({
      entity_type: 'user',
      entity_id: userEmail,
      entity_name: userEmail,
      action: 'login',
      user_email: userEmail,
      metadata: metadata
    });
  }

  async logUserLogout(userEmail: string): Promise<boolean> {
    return this.log({
      entity_type: 'user',
      entity_id: userEmail,
      entity_name: userEmail,
      action: 'logout',
      user_email: userEmail
    });
  }
}

// Instância singleton
export const auditLogger = new AuditLogger();

// Funções de conveniência para uso direto
export const logBusinessStatusChange = auditLogger.logBusinessStatusChange.bind(auditLogger);
export const logCreatorStatusChange = auditLogger.logCreatorStatusChange.bind(auditLogger);
export const logCampaignStatusChange = auditLogger.logCampaignStatusChange.bind(auditLogger);
export const logBusinessCreate = auditLogger.logBusinessCreate.bind(auditLogger);
export const logCreatorCreate = auditLogger.logCreatorCreate.bind(auditLogger);
export const logCampaignCreate = auditLogger.logCampaignCreate.bind(auditLogger);
export const logUserLogin = auditLogger.logUserLogin.bind(auditLogger);
export const logUserLogout = auditLogger.logUserLogout.bind(auditLogger);

export default auditLogger;

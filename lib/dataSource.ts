// Configuração da fonte de dados
// Permite alternar entre Google Sheets e Supabase

export const DATA_SOURCE = {
  // Altere para 'supabase' para usar o novo banco
  current: process.env.NEXT_PUBLIC_DATA_SOURCE || 'supabase', // 'sheets' ou 'supabase'
  
  // URLs das APIs
  apis: {
    sheets: {
      businesses: '/api/get-businesses',
      creators: '/api/get-creators', 
      campaigns: '/api/get-campaigns',
      creatorSlots: '/api/get-creator-slots'
    },
    supabase: {
      businesses: '/api/supabase/businesses',
      creators: '/api/supabase/creators',
      campaigns: '/api/supabase/campaigns',
      creatorSlots: '/api/supabase/creator-slots'
    }
  }
};

// Helper para obter URL da API baseada na fonte atual
export function getApiUrl(endpoint: keyof typeof DATA_SOURCE.apis.sheets): string {
  const source = DATA_SOURCE.current as 'sheets' | 'supabase';
  return DATA_SOURCE.apis[source][endpoint];
}

// Helper para verificar se está usando Supabase
export function isUsingSupabase(): boolean {
  return DATA_SOURCE.current === 'supabase';
}

// Helper para verificar se está usando Google Sheets
export function isUsingSheets(): boolean {
  return DATA_SOURCE.current === 'sheets';
}

// Função para fazer requisições com a fonte correta
export async function fetchFromDataSource(
  endpoint: keyof typeof DATA_SOURCE.apis.sheets,
  options?: RequestInit
): Promise<Response> {
  const url = getApiUrl(endpoint);
  
  console.log(`📡 Fazendo requisição para ${DATA_SOURCE.current.toUpperCase()}: ${url}`);
  
  return fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  });
}

// Função para buscar negócios (compatível com ambas as fontes)
export async function fetchBusinesses() {
  try {
    const response = await fetchFromDataSource('businesses');
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar negócios');
    }
    
    console.log(`✅ ${data.count} negócios carregados do ${DATA_SOURCE.current.toUpperCase()}`);
    return data.data;
    
  } catch (error) {
    console.error(`❌ Erro ao buscar negócios do ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Função para buscar criadores (compatível com ambas as fontes)
export async function fetchCreators() {
  try {
    const response = await fetchFromDataSource('creators');
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar criadores');
    }
    
    console.log(`✅ ${data.count} criadores carregados do ${DATA_SOURCE.current.toUpperCase()}`);
    return data.data;
    
  } catch (error) {
    console.error(`❌ Erro ao buscar criadores do ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Função para atualizar status de negócio
export async function updateBusinessStatus(businessId: string, newStatus: string, userEmail: string) {
  try {
    const response = await fetchFromDataSource('businesses', {
      method: 'POST',
      body: JSON.stringify({
        businessId,
        newStatus,
        userEmail
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao atualizar status');
    }
    
    console.log(`✅ Status atualizado no ${DATA_SOURCE.current.toUpperCase()}`);
    return data;
    
  } catch (error) {
    console.error(`❌ Erro ao atualizar status no ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Configuração da fonte de dados - APENAS SUPABASE
// Sistema migrado completamente para Supabase

export const DATA_SOURCE = {
  current: 'supabase' as const,
  
  apis: {
    supabase: {
      businesses: '/api/supabase/businesses',
      creators: '/api/supabase/creators',
      campaigns: '/api/supabase/campaigns',
      creatorSlots: '/api/supabase/creator-slots'
    }
  }
};

// Helper para obter URL da API
export function getApiUrl(endpoint: keyof typeof DATA_SOURCE.apis.supabase): string {
  const relativeUrl = DATA_SOURCE.apis.supabase[endpoint];

  // Se estamos no servidor (Node.js), usar URL absoluta
  const isServer = typeof window === 'undefined';
  if (isServer) {
    return `http://localhost:3000${relativeUrl}`;
  }

  return relativeUrl;
}

// Helper para verificar se está usando Supabase (sempre true agora)
export function isUsingSupabase(): boolean {
  return true;
}

// Helper para verificar se está usando Google Sheets (sempre false agora)
export function isUsingSheets(): boolean {
  return false;
}

// Função para buscar negócios do Supabase
export async function fetchBusinesses() {
  try {
    const response = await fetch(getApiUrl('businesses'));
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar negócios');
    }
    
    return data.data;
  } catch (error) {
    console.error('❌ Erro ao buscar negócios do Supabase:', error);
    throw error;
  }
}

// Função para buscar criadores do Supabase
export async function fetchCreators() {
  try {
    const response = await fetch(getApiUrl('creators'));
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar criadores');
    }
    
    return data.data;
  } catch (error) {
    console.error('❌ Erro ao buscar criadores do Supabase:', error);
    throw error;
  }
}

// Função para buscar campanhas do Supabase
export async function fetchCampaigns() {
  try {
    const response = await fetch(getApiUrl('campaigns'));
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar campanhas');
    }
    
    return data.data;
  } catch (error) {
    console.error('❌ Erro ao buscar campanhas do Supabase:', error);
    throw error;
  }
}

// Função para buscar jornada de campanhas do Supabase
export async function fetchCampaignJourney() {
  try {
    const campaigns = await fetchCampaigns();
    const businesses = await fetchBusinesses();

    // Transformar dados para formato de jornada
    return transformCampaignsToJourney(campaigns, businesses);
  } catch (error) {
    console.error('❌ Erro ao buscar jornada de campanhas do Supabase:', error);
    throw error;
  }
}

// Função auxiliar para transformar campanhas em formato de jornada
function transformCampaignsToJourney(campaigns: any[], businesses: any[]) {
  const businessMap = new Map(businesses.map(b => [b.id, b]));
  
  // Agrupar campanhas por business e mês
  const journeyMap = new Map();
  
  campaigns.forEach(campaign => {
    const business = businessMap.get(campaign.business_id);
    if (!business) return;
    
    const key = `${business.name}-${campaign.month}`;
    
    if (!journeyMap.has(key)) {
      journeyMap.set(key, {
        id: `journey_${campaign.id}`,
        business: business.name,
        businessId: business.id,
        mes: campaign.month,
        status: campaign.status || 'Reunião de briefing',
        criadores: [],
        campanhas: []
      });
    }
    
    const journeyItem = journeyMap.get(key);
    journeyItem.campanhas.push(campaign);
  });
  
  return Array.from(journeyMap.values());
}

export default {
  fetchBusinesses,
  fetchCreators,
  fetchCampaigns,
  fetchCampaignJourney,
  isUsingSupabase,
  isUsingSheets,
  getApiUrl
};

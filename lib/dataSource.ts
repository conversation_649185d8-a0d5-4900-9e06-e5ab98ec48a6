// Configuração da fonte de dados
// Permite alternar entre Google Sheets e Supabase

export const DATA_SOURCE = {
  // Altere para 'supabase' para usar o novo banco
  current: process.env.NEXT_PUBLIC_DATA_SOURCE || 'supabase', // 'sheets' ou 'supabase'
  
  // URLs das APIs
  apis: {
    sheets: {
      businesses: '/api/get-businesses',
      creators: '/api/get-creators', 
      campaigns: '/api/get-campaigns',
      creatorSlots: '/api/get-creator-slots'
    },
    supabase: {
      businesses: '/api/supabase/businesses',
      creators: '/api/supabase/creators',
      campaigns: '/api/supabase/campaigns',
      creatorSlots: '/api/supabase/creator-slots'
    }
  }
};

// Helper para obter URL da API baseada na fonte atual
export function getApiUrl(endpoint: keyof typeof DATA_SOURCE.apis.sheets): string {
  const source = DATA_SOURCE.current as 'sheets' | 'supabase';
  const relativeUrl = DATA_SOURCE.apis[source][endpoint];

  // Se estamos no servidor (Node.js), usar URL absoluta
  const isServer = typeof window === 'undefined';
  if (isServer) {
    return `http://localhost:3000${relativeUrl}`;
  }

  return relativeUrl;
}

// Helper para verificar se está usando Supabase
export function isUsingSupabase(): boolean {
  return DATA_SOURCE.current === 'supabase';
}

// Helper para verificar se está usando Google Sheets
export function isUsingSheets(): boolean {
  return DATA_SOURCE.current === 'sheets';
}

// Função para fazer requisições com a fonte correta
export async function fetchFromDataSource(
  endpoint: keyof typeof DATA_SOURCE.apis.sheets,
  options?: RequestInit
): Promise<Response> {
  const url = getApiUrl(endpoint);
  
  console.log(`📡 Fazendo requisição para ${DATA_SOURCE.current.toUpperCase()}: ${url}`);
  
  return fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  });
}

// Função para buscar negócios (compatível com ambas as fontes)
export async function fetchBusinesses() {
  try {
    const response = await fetchFromDataSource('businesses');
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar negócios');
    }
    
    console.log(`✅ ${data.count} negócios carregados do ${DATA_SOURCE.current.toUpperCase()}`);
    return data.data;
    
  } catch (error) {
    console.error(`❌ Erro ao buscar negócios do ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Função para buscar criadores (compatível com ambas as fontes)
export async function fetchCreators() {
  try {
    const response = await fetchFromDataSource('creators');
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar criadores');
    }

    console.log(`✅ ${data.count} criadores carregados do ${DATA_SOURCE.current.toUpperCase()}`);
    return data.data;

  } catch (error) {
    console.error(`❌ Erro ao buscar criadores do ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Função para buscar campanhas (compatível com ambas as fontes)
export async function fetchCampaigns() {
  try {
    const response = await fetchFromDataSource('campaigns');
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Erro ao buscar campanhas');
    }

    console.log(`✅ ${data.count} campanhas carregadas do ${DATA_SOURCE.current.toUpperCase()}`);
    return data.data;

  } catch (error) {
    console.error(`❌ Erro ao buscar campanhas do ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Função para buscar jornada de campanhas (compatível com ambas as fontes)
export async function fetchCampaignJourney() {
  try {
    if (isUsingSupabase()) {
      // Para Supabase, usar dados das campanhas e transformar para formato de jornada
      const campaigns = await fetchCampaigns();
      const businesses = await fetchBusinesses();

      // Transformar dados para formato de jornada
      return transformCampaignsToJourney(campaigns, businesses);
    } else {
      // Para Google Sheets, usar função original
      const { getCampaignJourneyData } = await import('@/app/actions/sheetsActions');
      return await getCampaignJourneyData();
    }
  } catch (error) {
    console.error(`❌ Erro ao buscar jornada de campanhas do ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

// Função auxiliar para transformar campanhas do Supabase em formato de jornada
function transformCampaignsToJourney(campaigns: any[], businesses: any[]) {
  const journeyMap = new Map<string, any>();

  // Criar mapa de negócios para busca rápida
  const businessMap = new Map<string, any>();
  businesses.forEach(business => {
    businessMap.set(business.id, business);
  });

  campaigns.forEach(campaign => {
    // Excluir campanhas finalizadas
    if (campaign.status?.toLowerCase() === 'finalizado' ||
        campaign.status?.toLowerCase() === 'finalizada') {
      return;
    }

    const businessName = campaign.businessName || 'Sem Negócio';
    const mes = campaign.mes || 'Sem Mês';
    const groupKey = `${businessName.toLowerCase()}-${mes.toLowerCase()}`;

    if (!journeyMap.has(groupKey)) {
      // Determinar estágio da jornada baseado no status
      let journeyStage: 'Reunião de briefing' | 'Agendamentos' | 'Entrega final' = 'Reunião de briefing';
      const statusLower = campaign.status?.toLowerCase() || '';

      if (statusLower === 'agendamentos' || statusLower === 'agendamento') {
        journeyStage = 'Agendamentos';
      } else if (statusLower === 'entrega final' || statusLower === 'entrega') {
        journeyStage = 'Entrega final';
      }

      journeyMap.set(groupKey, {
        id: groupKey,
        businessName: businessName,
        mes: mes,
        journeyStage: journeyStage,
        campanhas: [],
        totalCampanhas: 0,
        quantidadeCriadores: campaign.totalCriadores || 0,
        businessData: businessMap.get(campaign.businessId),
        campaignIds: [],
        primaryCampaignId: undefined
      });
    }

    const journey = journeyMap.get(groupKey)!;

    // Adicionar campanha ao grupo
    journey.campanhas.push(campaign);
    journey.totalCampanhas++;
    journey.quantidadeCriadores = Math.max(journey.quantidadeCriadores, campaign.totalCriadores || 0);

    // Adicionar Campaign_ID ao array de IDs
    if (campaign.id && !journey.campaignIds.includes(campaign.id)) {
      journey.campaignIds.push(campaign.id);

      // Definir o primeiro Campaign_ID como primário
      if (!journey.primaryCampaignId) {
        journey.primaryCampaignId = campaign.id;
      }
    }
  });

  const result = Array.from(journeyMap.values()).sort((a, b) => {
    // Ordenar por business name
    return a.businessName.localeCompare(b.businessName);
  });

  console.log(`✅ ${result.length} campanhas na jornada (Supabase, excluindo finalizadas)`);
  return result;
}

// Função para atualizar status de negócio
export async function updateBusinessStatus(businessId: string, newStatus: string, userEmail: string) {
  try {
    const response = await fetchFromDataSource('businesses', {
      method: 'POST',
      body: JSON.stringify({
        businessId,
        newStatus,
        userEmail
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Erro ao atualizar status');
    }
    
    console.log(`✅ Status atualizado no ${DATA_SOURCE.current.toUpperCase()}`);
    return data;
    
  } catch (error) {
    console.error(`❌ Erro ao atualizar status no ${DATA_SOURCE.current.toUpperCase()}:`, error);
    throw error;
  }
}

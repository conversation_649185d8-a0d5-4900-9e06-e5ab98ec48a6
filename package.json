{"name": "crm-interno", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate-passwords": "tsx scripts/migrate-passwords.ts", "verify-passwords": "tsx scripts/migrate-passwords.ts verify", "security-audit": "npm audit --audit-level moderate", "migrate-from-sheets": "tsx scripts/migrate-from-sheets.ts", "migrate-simple": "tsx scripts/migrate-simple.ts", "migrate-campaigns": "tsx scripts/migrate-campaigns-complete.ts", "fix-campaign-creators": "tsx scripts/fix-campaign-creators.ts", "migrate-no-audit": "tsx scripts/disable-audit-and-migrate.ts", "test-simple-insert": "tsx scripts/simple-campaign-creators-insert.ts", "test-supabase": "tsx scripts/test-supabase-connection.ts", "verify-migration": "tsx scripts/verify-migration.ts", "supabase:types": "supabase gen types typescript --local > lib/database.types.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@supabase/supabase-js": "^2.51.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "dotenv": "^16.4.5", "firebase": "^11.10.0", "googleapis": "^152.0.0", "moment": "^2.30.1", "next": "15.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "zod": "^3.22.4", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8.4.38", "tailwind-material-colors": "^3.2.3", "tailwindcss": "^3.4.17", "tsx": "^4.7.0", "typescript": "^5"}}